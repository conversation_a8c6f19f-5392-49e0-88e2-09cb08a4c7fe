# 移除所有调试代码
# 在文件开头的调试输出

from django.urls import path, re_path, include
from django.views.generic import TemplateView
from django.contrib import admin
from django.views.decorators.cache import cache_page
from demo_api import settings
from django.conf.urls.static import static
from api.views import tcmNLP, tcmchat
from api.consumers import TestStreamConsumer
from ninja import NinjaAPI
from api.views import bank
from .ninja_apis import (
    bank_api, doubao_aichat, invite_api, questionnaire_api, 
    db_health_api, auth_api, async_bank_api,
    routertest1_api, routertest2_api, routertest3_api  # 🆕 测试API模块
)
from api.views import abstractDAKA
from api.views.db_admin import db_pool_monitor_view  # 导入数据库连接池监控视图
from api.views.daily_tcm_questions import DailyTCMQuestionView, DailyQuizScoreView, DailyQuizRankingView  # 导入新的每日题目视图
from api.views.tcm_books_api import (  # 导入新的优化书籍API
    OptimizedBookListView,
    BookDetailView,
    BookChaptersView,
    BookSearchView,
    ChapterContentView,
    BookCacheManagementView,
    BookDownloadInfoView,
    BookDownloadView,
    BatchBookDownloadView
)

# 🎯 预后系统API现在从ninja_apis/__init__.py统一管理，无需在此重复定义

websocket_urlpatterns = [
    re_path(r'^ws/test/$', TestStreamConsumer.as_asgi()),
]

urlpatterns = [
    re_path(r'^bank/DeleteUserActivityView/$', bank.DeleteUserActivityView.as_view()),
    re_path(r'^bank/AbstractGoalView/$', abstractDAKA.AbstractGoalView.as_view()),

    re_path(r'^bank/AbstractGoalDetail/(?P<goal_id>\d+)/$', abstractDAKA.AbstractGoalDetailView.as_view()),
    re_path(r'^bank/AbstractGoalRecordView/$', bank.AbstractGoalRecordView.as_view()),#这个接口没用到
    re_path(r'^bank/DayActivitiesView/$', bank.DayActivitiesView.as_view()),
    re_path(r'^bank/UserCardView/$', bank.UserCardView.as_view()),
    re_path(r'^bank/CreateCustomActivityView/$', bank.CreateCustomActivityView.as_view()),
    re_path(r'^bank/UserGoalView/$', bank.UserGoalView.as_view()),
    re_path(r'^bank/ActivityListView/$', bank.ActivityListView.as_view()),
    re_path(r'^bank/BadHabitListView/$', bank.BadHabitListView.as_view()),
    re_path(r'^bank/UserStatsView/$', bank.UserStatsView.as_view()),
    re_path(r'^bank/UserCardMonthView/$', bank.UserCardMonthView.as_view()),#更新日历,

    # 数据库连接池监控
    path('db-pool-monitor/', db_pool_monitor_view, name='db_pool_monitor'),

    # Ninja APIs from ninja_apis/__init__.py - 防重复导入版本
    path("bank/", bank_api.urls),
    path("doubao_aichat/", doubao_aichat.urls),
    path("invite/", invite_api.urls),
    path("questionnaire/", questionnaire_api.urls),
    path("db-health/", db_health_api.urls),
    path("auth/", auth_api.urls),
    path("async-bank/", async_bank_api.urls),
    
    # 🆕 新增的测试API模块 - 无意义命名避免误会
    path("routertest1/", routertest1_api.urls),
    path("routertest2/", routertest2_api.urls),
    path("routertest3/", routertest3_api.urls),

    re_path(r'^bank/EmptyRequestView/$', bank.EmptyRequestView.as_view()),

    path('test-async/', tcmchat.AsyncTestView.as_view(), name='test-async'),

    path('tcmchat/StreamingDeepseekAnalysisView/', tcmchat.StreamingDeepseekAnalysisView.as_view(), name='StreamingDeepseekAnalysisView'),
    path('tcmchat/get_all_symptoms/', tcmchat.get_all_symptoms, name='get_all_symptoms'),
    path('tcmchat/get_custom_symptoms/', tcmchat.get_custom_symptoms, name='get_custom_symptoms'),
    path('tcmchat/delete_custom_symptom/<int:symptom_id>/', tcmchat.delete_custom_symptom, name='delete_custom_symptom'),
    path('tcmchat/add_custom_symptom/',tcmchat.add_custom_symptom),

    path('clear-member/', tcmchat.clear_member_status, name='clear-member'),

    path('wechatV3_pay/', tcmchat.wechatV3_pay, name='pay'),
    path('wechatV3_pay_app/', tcmchat.wechatV3_pay_app, name='pay_app'),
    path('wechatV3_notify/', tcmchat.wechatV3_notify, name='notify'),
    path('tcmNLP/analysis_with_deepseek_For_Now_advice/', tcmchat.analysis_with_deepseek_For_Now_advice.as_view(), name='123'),

    path('tcmNLP/Only_analysis_with_deepseek_symptoms/', tcmchat.Only_analysis_with_deepseek_symptoms.as_view(), name='444'),

    path('bank/delete_all_symptoms/', bank.delete_all_symptoms, name='delete_all_symptoms'),

    path('bank/get_all_symptoms/', bank.get_all_symptoms, name='get_all_symptoms'),

    path('bank/update_symptoms/', bank.update_symptoms, name='update_symptoms'),
    path('tcmNLP/ai_analysis_symptoms/', tcmchat.analysis_with_deepseek_symptoms.as_view(), name='aichat_symptoms_analysis'), # Renamed for clarity

    path('tcmNLP/ai_analysis/', tcmchat.analysis_with_deepseek.as_view(), name='aichat_analysis'), # Renamed for clarity

    path('tcmchat/report/', tcmchat.create_report, name='create_report'),

    path('delete-account/', tcmchat.delete_account, name='delete_account'),

    # 保留原有的refresh-token路由以确保向后兼容
    re_path(r'^bank/refresh-token/$', bank.RefreshTokenView.as_view()),

    path('tcmNLP/aichat-legacy/', tcmchat.chat_with_douban.as_view(), name='aichat_legacy_douban'), # Renamed for clarity

    path('feedbacks/create/', tcmNLP.create_feedback, name='create_feedback'),
    path('feedbacks/list/', tcmNLP.list_feedbacks, name='list_feedbacks'),
    path('feedbacks/user/', tcmNLP.get_user_feedbacks, name='get_user_feedbacks'),
    path('active-announcement/', bank.ActiveAnnouncementView.as_view(), name='active_announcement'),

    re_path(r'^logout/$', bank.LogoutView.as_view()), # Note: Ninja logout might be different

    re_path(r'^tcmNLP/OwnAIdiet/$', tcmNLP.OwnAIdiet.as_view()),
    
    # 新的优化书籍API路由
    path('tcmNLP/v2/books/', OptimizedBookListView.as_view(), name='optimized-book-list'),
    path('tcmNLP/v2/books/<str:book_id>/', BookDetailView.as_view(), name='book-detail'),
    path('tcmNLP/v2/books/<str:book_id>/chapters/', BookChaptersView.as_view(), name='book-chapters'),
    path('tcmNLP/v2/books/<str:book_id>/content/<str:file_name>', ChapterContentView.as_view(), name='optimized-chapter-content'),
    path('tcmNLP/v2/search/', BookSearchView.as_view(), name='optimized-book-search'),
    path('tcmNLP/v2/cache/', BookCacheManagementView.as_view(), name='book-cache-management'),
    
    # 书籍下载API
    path('tcmNLP/v2/books/<str:book_id>/download-info/', BookDownloadInfoView.as_view(), name='book-download-info'),
    path('tcmNLP/v2/books/<str:book_id>/download/', BookDownloadView.as_view(), name='book-download'),
    path('tcmNLP/v2/download/batch/', BatchBookDownloadView.as_view(), name='batch-book-download'),

    # 保留原有的书籍API路由以确保向后兼容
    re_path(r'^tcmNLP/search/$', tcmNLP.BookSearchView.as_view(), name='book-search-legacy'),
    path('tcmNLP/books-legacy/<str:book_id>/<str:file_name>', tcmNLP.ChapterContentView.as_view(), name='chapter-content-legacy'),
    re_path(r'^tcmNLP/books-legacy/$', tcmNLP.BookListView.as_view(), name='book_list_legacy'),
    re_path(r'^tcmNLP/books-legacy/<slug:book_slug>/$', tcmNLP.BookContentView.as_view(), name='book_detail_legacy'),
    
    re_path(r'^tcmNLP/get_solar_term/$', tcmNLP.get_solar_term),
    # re_path(r'^tcmNLP/get_current_season/$', tcmNLP.get_current_season666),
    re_path(r'^tcmNLP/CheckPaymentStatusView/$', tcmNLP.CheckPaymentStatusView.as_view()),

    re_path(r'^tcmNLP/AlipayAuthCallbackView/$', tcmNLP.AlipayAuthCallbackView.as_view()),

    re_path(r'^tcmNLP/AlipayView/$', tcmNLP.AlipayView.as_view()),
    re_path(r'^tcmNLP/callback/$', tcmNLP.AlipayAuthCallbackView.as_view(), name="alipay-callback-legacy"), # Added name

    re_path(r'^tcmNLP/TotalWeightsView/$', tcmNLP.TotalWeightsView.as_view()),

    re_path(r'^tcmNLP/get_current_season/$', tcmNLP.get_current_tcm_season),

    re_path(r'^tcmNLP/ganzhi/$', tcmNLP.get_ganzhi),

    re_path(r'^tcmNLP/NLPdiet/$', tcmNLP.NLPdiet.as_view()),
    path('terms/', bank.terms, name='terms'),
    path('privacy/', bank.privacy, name='privacy'),
    path('contact/', bank.contact, name='contact'),
    re_path(r'^bank/activate_membership/$', bank.ActivateMembershipView.as_view()),
    re_path(r'^bank/checkmembershipView/$',bank.checkmembershipView.as_view()),
    re_path(r'^bank/WeChatLoginView/$',bank.WeChatLoginView.as_view()),
    re_path(r'^bank/CheckOrRegisterUserView/$',bank.CheckOrRegisterUserView.as_view()),
    re_path(r'^bank/AddSharePointsView/$',bank.AddSharePointsView.as_view()),
    re_path(r'^bank/AcupointListView/$',bank.AcupointListView.as_view()),
    re_path(r'^bank/AcupointDetailView/$',bank.AcupointDetailView.as_view()),
    # re_path(r'^bank/questionnaires/$', bank.QuestionnaireListView.as_view()), 
    re_path(r'^bank/questionnaire/$', bank.QuestionnaireDetailView.as_view()), 
    re_path(r'^bank/save_questionnaire/$', bank.SaveQuestionnaireView.as_view()),
    re_path(r'^bank/GetUserInfoView/$', bank.GetUserInfoView.as_view()),
    re_path(r'^bank/check_questionnaire_filled/$', bank.CheckQuestionnaireFilledView.as_view()),
    re_path(r'^bank/GetUserQuestionnairesView/$', bank.GetUserQuestionnairesView.as_view()),#读取用户问卷填写情况

    re_path(r'^bank/CalculateScoresView/$', bank.CalculateScoresView.as_view()),#后端计算体质问卷得分
    
    re_path(r'^bank/UserCardView/$', bank.UserCardView.as_view()),#日历系统
    
    re_path(r'^bank/check_points/$', bank.check_points.as_view()),#检查积分
    re_path(r'^bank/HealthExpRankingView/$', bank.HealthExpRankingView.as_view()),
    re_path(r'^bank/MemberExpRankingView/$', bank.MemberExpRankingView.as_view()),
    
    # 新的每日中医题目API - 使用优化的视图
    re_path(r'^bank/DailyTCMQuestionViewV2/$', DailyTCMQuestionView.as_view()),  # 每日题库 - 新版本
    re_path(r'^bank/DailyQuizScoreViewV2/$', DailyQuizScoreView.as_view()),  # 上传分数 - 新版本
    re_path(r'^bank/DailyQuizRankingViewV2/$', DailyQuizRankingView.as_view()),  # 答题分数排行榜 - 新版本
    
    # 保留原有路由以确保向后兼容
    re_path(r'^bank/DailyTCMQuestionView/$', bank.DailyTCMQuestionView.as_view(), name="daily-tcm-question-legacy"),
    re_path(r'^bank/DailyQuizScoreView/$', bank.DailyQuizScoreView.as_view(), name="daily-quiz-score-legacy"),
    re_path(r'^bank/DailyQuizRankingView/$', bank.DailyQuizRankingView.as_view(), name="daily-quiz-ranking-legacy"),
    path('admineee-riyuetcm-123/', admin.site.urls),
    re_path(r'^bank/user-create/$', bank.UserCreateView.as_view(), name='api-user-register'),
    re_path(r'^bank/token-obtain/$', bank.UIDTokenObtainPairView.as_view(), name='token_obtain_pair'),
    re_path(r'^bank/token-refresh/$', bank.CustomTokenRefreshView.as_view(), name='token_refresh'),
    # re_path(r'^HomePage/', bank.HomeView.as_view()),
    path('HomePage/', cache_page(60 * 60 * 2)(TemplateView.as_view(template_name='web/home.html')), name='home'),  # 缓存2小时
    re_path(r'^test/wechat-login11/$', bank.LoginByWeixinView_new1.as_view()),#微信一键注册登陆返回token
    re_path(r'^test/register_or_login_by_phone/$', bank.RegisterOrLoginByPhoneView.as_view()),#手机号一键注册登陆
    
    # 新的异步登录API路由 - 保持兼容性同时提供高性能版本
    # 新URL: /api/auth/wechat-login (异步，高性能)
    # 新URL: /api/auth/phone-login (异步，高性能)
    # 老URL: /api/test/wechat-login11/ (同步，向后兼容)
    # 老URL: /api/test/register_or_login_by_phone/ (同步，向后兼容)
    
    re_path(r'^bank/AnalyzeQuestionnaireView/$', bank.AnalyzeQuestionnaireView.as_view()),#导航栏问卷计算
    re_path(r'^bank/AnalyzeQuestionnaireViewcomplex/$', bank.AnalyzeQuestionnaireViewcomplex.as_view()),#导航栏问卷计算
    
    path('logout-view/', bank.LogoutView.as_view(), name='logout_view'), # Renamed to avoid conflict
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# 移除文件末尾的调试代码