#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发性能测试脚本 - 测试缓存优化后的API性能
重点测试：HomePage、问卷计算、预后分析等高频访问组件
"""

import asyncio
import aiohttp
import time
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
CONCURRENT_USERS = [1, 5, 10, 20, 50]  # 并发用户数
TEST_DURATION = 30  # 每个测试持续时间（秒）

# 从token文件读取测试token
def load_test_token():
    try:
        with open('test_token_user_2.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("⚠️ 未找到test_token_user_2.txt文件，将使用无token测试")
        return None

TEST_TOKEN = load_test_token()

# 测试端点配置
TEST_ENDPOINTS = {
    "HomePage": {
        "url": f"{BASE_URL}/api/HomePage/",
        "method": "GET",
        "headers": {},
        "expected_cache": True,
        "description": "首页静态内容（已优化缓存2小时）"
    },
    "问卷计算": {
        "url": f"{BASE_URL}/api/questionnaire_api/calculate_constitution_indicators/",
        "method": "POST",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}", "Content-Type": "application/json"} if TEST_TOKEN else {"Content-Type": "application/json"},
        "data": {},
        "expected_cache": True,
        "description": "体质计算API（计算密集型）"
    },
    "疗法列表": {
        "url": f"{BASE_URL}/api/routertest1/prognosis/therapies?page=1&size=20",
        "method": "GET", 
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "expected_cache": True,
        "description": "疗法列表API（多层缓存）"
    },
    "中医题目": {
        "url": f"{BASE_URL}/api/daily_tcm_question/random-questions/?limit=10",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "expected_cache": True,
        "description": "随机中医题目（30分钟缓存）"
    },
    "数据库健康": {
        "url": f"{BASE_URL}/api/db_health_api/connection-pool-status/",
        "method": "GET",
        "headers": {"Authorization": f"Bearer {TEST_TOKEN}"} if TEST_TOKEN else {},
        "expected_cache": False,
        "description": "数据库连接池状态（实时数据）"
    }
}

class PerformanceTestResult:
    def __init__(self):
        self.response_times = []
        self.success_count = 0
        self.error_count = 0
        self.cache_hits = 0
        self.start_time = None
        self.end_time = None
        self.errors = []
        
    def add_result(self, response_time, success, cache_hit=False, error_msg=None):
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
            if cache_hit:
                self.cache_hits += 1
        else:
            self.error_count += 1
            if error_msg:
                self.errors.append(error_msg)
    
    def get_stats(self):
        if not self.response_times:
            return {}
            
        total_requests = len(self.response_times)
        duration = (self.end_time - self.start_time) if self.start_time and self.end_time else 0
        
        return {
            "total_requests": total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": (self.success_count / total_requests * 100) if total_requests > 0 else 0,
            "cache_hit_rate": (self.cache_hits / self.success_count * 100) if self.success_count > 0 else 0,
            "avg_response_time": statistics.mean(self.response_times),
            "min_response_time": min(self.response_times),
            "max_response_time": max(self.response_times),
            "median_response_time": statistics.median(self.response_times),
            "p95_response_time": statistics.quantiles(self.response_times, n=20)[18] if len(self.response_times) >= 20 else max(self.response_times),
            "requests_per_second": total_requests / duration if duration > 0 else 0,
            "test_duration": duration
        }

async def make_request(session, endpoint_name, config):
    """发送单个HTTP请求"""
    start_time = time.time()
    try:
        if config["method"] == "GET":
            async with session.get(config["url"], headers=config.get("headers", {})) as response:
                await response.text()
                response_time = time.time() - start_time
                
                # 检查是否命中缓存
                cache_hit = False
                if "Cache-Control" in response.headers or "ETag" in response.headers:
                    cache_hit = True
                
                return response_time, response.status == 200, cache_hit, None
                
        elif config["method"] == "POST":
            async with session.post(
                config["url"], 
                headers=config.get("headers", {}),
                json=config.get("data", {})
            ) as response:
                await response.text()
                response_time = time.time() - start_time
                
                cache_hit = False
                if "Cache-Control" in response.headers:
                    cache_hit = True
                    
                return response_time, response.status == 200, cache_hit, None
                
    except Exception as e:
        response_time = time.time() - start_time
        return response_time, False, False, str(e)

async def run_concurrent_test(endpoint_name, config, concurrent_users, duration):
    """运行并发测试"""
    print(f"\n🚀 开始测试 {endpoint_name} - {concurrent_users} 并发用户，持续 {duration} 秒")
    print(f"   📝 {config['description']}")
    
    result = PerformanceTestResult()
    result.start_time = time.time()
    
    # 创建HTTP会话
    timeout = aiohttp.ClientTimeout(total=30)
    connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
    
    async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
        # 创建并发任务
        tasks = []
        end_time = time.time() + duration
        
        async def worker():
            while time.time() < end_time:
                response_time, success, cache_hit, error_msg = await make_request(session, endpoint_name, config)
                result.add_result(response_time, success, cache_hit, error_msg)
                await asyncio.sleep(0.1)  # 避免过于频繁的请求
        
        # 启动并发工作者
        for _ in range(concurrent_users):
            tasks.append(asyncio.create_task(worker()))
        
        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
    
    result.end_time = time.time()
    return result

def print_test_results(endpoint_name, concurrent_users, result):
    """打印测试结果"""
    stats = result.get_stats()
    if not stats:
        print(f"❌ {endpoint_name} ({concurrent_users}并发) - 无有效数据")
        return
    
    print(f"\n📊 {endpoint_name} 测试结果 ({concurrent_users} 并发用户):")
    print(f"   ✅ 总请求数: {stats['total_requests']}")
    print(f"   ✅ 成功率: {stats['success_rate']:.1f}%")
    print(f"   ⚡ 缓存命中率: {stats['cache_hit_rate']:.1f}%")
    print(f"   ⏱️  平均响应时间: {stats['avg_response_time']*1000:.1f}ms")
    print(f"   ⏱️  中位数响应时间: {stats['median_response_time']*1000:.1f}ms")
    print(f"   ⏱️  95%响应时间: {stats['p95_response_time']*1000:.1f}ms")
    print(f"   🔥 QPS: {stats['requests_per_second']:.1f}")
    
    if result.errors:
        print(f"   ❌ 错误样例: {result.errors[:3]}")

async def main():
    """主测试函数"""
    print("🎯 Django API 并发性能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 测试地址: {BASE_URL}")
    print(f"🔑 使用Token: {'是' if TEST_TOKEN else '否'}")
    print(f"👥 并发用户数: {CONCURRENT_USERS}")
    print(f"⏰ 每轮测试时长: {TEST_DURATION}秒")
    
    # 存储所有测试结果
    all_results = {}
    
    # 对每个端点进行测试
    for endpoint_name, config in TEST_ENDPOINTS.items():
        print(f"\n{'='*60}")
        print(f"🎯 测试端点: {endpoint_name}")
        print(f"📍 URL: {config['url']}")
        
        endpoint_results = {}
        
        # 对每个并发级别进行测试
        for concurrent_users in CONCURRENT_USERS:
            try:
                result = await run_concurrent_test(endpoint_name, config, concurrent_users, TEST_DURATION)
                endpoint_results[concurrent_users] = result
                print_test_results(endpoint_name, concurrent_users, result)
                
                # 测试间隔，让系统恢复
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"❌ {endpoint_name} ({concurrent_users}并发) 测试失败: {e}")
        
        all_results[endpoint_name] = endpoint_results
    
    # 生成汇总报告
    print_summary_report(all_results)

def print_summary_report(all_results):
    """打印汇总报告"""
    print(f"\n{'='*60}")
    print("📈 性能测试汇总报告")
    print("="*60)
    
    for endpoint_name, endpoint_results in all_results.items():
        print(f"\n🎯 {endpoint_name}:")
        print("   并发数 | QPS   | 平均响应时间 | 缓存命中率 | 成功率")
        print("   -------|-------|-------------|-----------|-------")
        
        for concurrent_users, result in endpoint_results.items():
            stats = result.get_stats()
            if stats:
                print(f"   {concurrent_users:6d} | {stats['requests_per_second']:5.1f} | {stats['avg_response_time']*1000:8.1f}ms | {stats['cache_hit_rate']:8.1f}% | {stats['success_rate']:5.1f}%")

if __name__ == "__main__":
    asyncio.run(main())
